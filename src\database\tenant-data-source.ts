import { DataSource, DataSourceOptions } from 'typeorm';
import * as dotenv from 'dotenv';
import { Client } from 'pg';

dotenv.config();

/**
 * Tenant Database Migration System
 *
 * This file provides multiple ways to run tenant database migrations:
 *
 * 1. **Single Tenant Migration (TypeORM CLI)**:
 *    - Windows: $env:TENANT_DB="tenant_demo_one"; npm run migration:run:tenant
 *    - Linux/Mac: TENANT_DB=tenant_demo_one npm run migration:run:tenant
 *
 * 2. **All Tenants Migration (Custom Function)**:
 *    - npm run migration:run:tenant:all
 *
 * 3. **Cross-Platform Helper Script**:
 *    - npm run migration:tenant (shows available tenants)
 *    - npm run migration:tenant tenant_demo_one (migrate specific tenant)
 *    - npm run migration:tenant all (migrate all tenants)
 *
 * The TypeORM CLI approach is preferred for individual tenant operations
 * as it provides full TypeORM CLI functionality (run, revert, show, etc.)
 */

/**
 * Gets the TypeORM configuration for a tenant database
 * @param databaseName The name of the tenant database
 * @returns DataSourceOptions configuration
 */
export const getTenantDataSourceConfig = (databaseName: string): DataSourceOptions => ({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: databaseName,
  entities: [
    __dirname + '/../modules/products/entities/*.entity{.ts,.js}',
    __dirname + '/../modules/orders/entities/*.entity{.ts,.js}',
    __dirname + '/../modules/categories/entities/*.entity{.ts,.js}',
  ],
  migrations: [__dirname + '/migrations/tenant/*{.ts,.js}'],
  synchronize: false,
  logging: process.env.DB_LOGGING === 'true',
});

/**
 * Gets the PostgreSQL client configuration for connecting to databases
 * @param databaseName Optional database name (defaults to main database)
 * @returns Client configuration
 */
const getPgClientConfig = (databaseName?: string) => ({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT ? parseInt(process.env.DB_PORT, 10) : 5432,
  user: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: databaseName || process.env.DB_MAIN_DATABASE || 'postgres',
});

/**
 * Migrates all tenant databases listed in the vendors table (main DB),
 * only if not already migrated.
 */
export async function migrateAllTenants() {
  console.log('Starting tenant migrations...');

  // 1. Connect to main DB and get all tenant database names
  const mainClient = new Client(getPgClientConfig());
  try {
    await mainClient.connect();
    const res = await mainClient.query(
      'SELECT "databaseName" FROM vendors WHERE "databaseName" IS NOT NULL'
    );
    const tenantDbs = res.rows.map(row => row.databaseName);

    if (tenantDbs.length === 0) {
      console.log('No tenant databases found to migrate.');
      return;
    }

    console.log(`Found ${tenantDbs.length} tenant databases to process`);

    // 2. For each tenant DB, check if migrations have run, and run if not
    for (const dbName of tenantDbs) {
      const tenantClient = new Client(getPgClientConfig(dbName));
      try {
        await tenantClient.connect();

        // Check if migrations table exists and has entries
        const tableRes = await tenantClient.query(
          `SELECT to_regclass('public.migrations') as exists`
        );

        let alreadyMigrated = false;
        if (tableRes.rows[0].exists) {
          const migRes = await tenantClient.query(
            'SELECT COUNT(*) FROM migrations'
          );
          alreadyMigrated = parseInt(migRes.rows[0].count, 10) > 0;
        }

        if (alreadyMigrated) {
          console.log(`✓ Migrations already ran for tenant: ${dbName}`);
          continue;
        }

        // Run migrations for this tenant DB
        console.log(`⌛ Running migrations for tenant: ${dbName}`);
        const dataSource = new DataSource(getTenantDataSourceConfig(dbName));

        try {
          await dataSource.initialize();
          await dataSource.runMigrations();
          console.log(`✓ Migrations completed for tenant: ${dbName}`);
        } finally {
          await dataSource.destroy();
        }
      } catch (err) {
        console.error(`✗ Migration failed for tenant: ${dbName}`, err);
      } finally {
        await tenantClient.end();
      }
    }
  } catch (err) {
    console.error('Failed to process tenant migrations:', err);
    throw err;
  } finally {
    await mainClient.end();
  }

  console.log('✅ All tenant migrations processed');
}

/**
 * Helper function to run the migrations from command line
 */
export async function runTenantMigrations() {
  try {
    await migrateAllTenants();
    process.exit(0);
  } catch (error) {
    console.error('❌ Tenant migration failed:', error);
    process.exit(1);
  }
}

// Execute if run directly (for command line usage)
if (require.main === module) {
  runTenantMigrations();
}

/**
 * For TypeORM CLI usage with tenant databases.
 * Use TENANT_DB environment variable to specify which tenant database to target.
 * If TENANT_DB is not set, it will use a default configuration.
 */
const getTenantDatabaseName = (): string => {
  if (process.env.TENANT_DB) {
    return process.env.TENANT_DB;
  }

  // If no specific tenant is specified, we'll use the first available tenant database
  // This is mainly for CLI operations where a specific tenant needs to be targeted
  console.warn('No TENANT_DB environment variable set. Use TENANT_DB=<database_name> to target a specific tenant.');
  return 'tenant_demo_one'; // Default fallback
};

export const AppDataSource = new DataSource(getTenantDataSourceConfig(getTenantDatabaseName()));