{"info": {"name": "Multi-Tenant-Ecommerce-API", "description": "API collection for the Multi-Tenant E-commerce platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Authentication endpoints for different user roles", "item": [{"name": "Super Admin", "item": [{"name": "Super Admin Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/superadmin/login", "path": ["auth", "superadmin", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}}}, {"name": "Register Super Admin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/superadmin/register", "path": ["auth", "superadmin", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"passwordConfirmation\": \"password123\"\n}"}}}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/vendor/login", "path": ["auth", "vendor", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}}}, {"name": "Register Vendor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/vendor/register", "path": ["auth", "vendor", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"passwordConfirmation\": \"password123\",\n  \"storeName\": \"My Store\",\n  \"description\": \"My store description\",\n  \"subdomain\": \"mystore\"\n}"}}}]}, {"name": "Customer", "item": [{"name": "Customer <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/customer/login", "path": ["auth", "customer", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}}}, {"name": "Register Customer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/customer/register", "path": ["auth", "customer", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"passwordConfirmation\": \"password123\"\n}"}}}]}, {"name": "Common", "item": [{"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/refresh", "path": ["auth", "refresh"]}, "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"your-refresh-token\"\n}"}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "path": ["auth", "logout"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/auth/profile", "path": ["auth", "profile"]}}}]}]}, {"name": "Products", "description": "Product management endpoints", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products", "path": ["products"]}}}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/:id", "path": ["products", ":id"], "variable": [{"key": "id", "value": ""}]}}}, {"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/products", "path": ["products"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Product Name\",\n  \"description\": \"Product Description\",\n  \"price\": 99.99,\n  \"stock\": 100,\n  \"categoryId\": \"category-uuid\",\n  \"images\": [\"image1.jpg\", \"image2.jpg\"]\n}"}}}, {"name": "Update Product", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/products/:id", "path": ["products", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Product Name\",\n  \"price\": 149.99,\n  \"stock\": 200\n}"}}}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/products/:id", "path": ["products", ":id"], "variable": [{"key": "id", "value": ""}]}}}]}, {"name": "Categories", "description": "Category management endpoints", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories", "path": ["categories"]}}}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/:id", "path": ["categories", ":id"], "variable": [{"key": "id", "value": ""}]}}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/categories", "path": ["categories"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Category Name\",\n  \"description\": \"Category Description\",\n  \"image\": \"category-image.jpg\"\n}"}}}, {"name": "Update Category", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/categories/:id", "path": ["categories", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Category Name\",\n  \"description\": \"Updated Category Description\"\n}"}}}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/categories/:id", "path": ["categories", ":id"], "variable": [{"key": "id", "value": ""}]}}}, {"name": "Get All Subcategories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/subcategories", "path": ["categories", "subcategories"]}}}, {"name": "Get Subcategory by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/subcategories/:id", "path": ["categories", "subcategories", ":id"], "variable": [{"key": "id", "value": ""}]}}}, {"name": "Create Subcategory", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/categories/subcategories", "path": ["categories", "subcategories"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Subcategory Name\",\n  \"description\": \"Subcategory Description\",\n  \"categoryId\": \"parent-category-uuid\",\n  \"image\": \"subcategory-image.jpg\"\n}"}}}, {"name": "Update Subcategory", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/categories/subcategories/:id", "path": ["categories", "subcategories", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Subcategory Name\",\n  \"description\": \"Updated Subcategory Description\"\n}"}}}, {"name": "Delete Subcategory", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/categories/subcategories/:id", "path": ["categories", "subcategories", ":id"], "variable": [{"key": "id", "value": ""}]}}}]}, {"name": "Tenants", "description": "Tenant management endpoints", "item": [{"name": "Get All Tenants", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/tenants", "path": ["tenants"]}}}, {"name": "Get Tenant by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/tenants/:id", "path": ["tenants", ":id"], "variable": [{"key": "id", "value": ""}]}}}]}, {"name": "Debug", "description": "Debug endpoints", "item": [{"name": "Get Tenant Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/debug/tenant", "path": ["debug", "tenant"]}}}]}]}