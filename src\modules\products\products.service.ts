import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { TenantsService } from '../tenants/tenants.service';
import { Product } from './entities/product.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { TenantConnection } from '../../common/interfaces/tenant-connection.interface';

@Injectable()
export class ProductsService {
  constructor(private readonly tenantsService: TenantsService) {}

  async create(createProductDto: CreateProductDto, tenantId: string, vendorId: string): Promise<Product> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Product);

    const product = repository.create({
      ...createProductDto,
      vendorId,
    });

    return repository.save(product);
  }

  async findAll(tenantId?: string): Promise<Product[]> {
    if (tenantId) {
      // Get products for specific tenant
      const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
      const repository = tenantConnection.dataSource.getRepository(Product);
      return repository.find({
        where: { isActive: true },
        order: { createdAt: 'DESC' },
        relations: ['category'],
      });
    } else {
      // Get products from all tenants (main catalog)
      const allTenants = await this.tenantsService.findAll();
      const allProducts: Product[] = [];

      for (const vendor of allTenants) {
        if (!vendor.isActive) continue;

        try {
          const tenantConnection = await this.tenantsService.getTenantConnection(vendor.subdomain);
          const repository = tenantConnection.dataSource.getRepository(Product);
          const products = await repository.find({
            where: { isActive: true },
            order: { createdAt: 'DESC' },
            relations: ['category'],
          });
          allProducts.push(...products);
        } catch (error) {
          console.error(`Error fetching products for tenant ${vendor.subdomain}:`, error);
          // Continue with next tenant
        }
      }

      return allProducts;
    }
  }

  async findOne(id: string, tenantId: string): Promise<Product> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Product);

    const product = await repository.findOne({
      where: { id },
      relations: ['category'],
    });

    if (!product) {
      throw new NotFoundException(`Product with ID "${id}" not found`);
    }

    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto, tenantId: string, vendorId: string): Promise<Product> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Product);

    const product = await repository.findOne({
      where: { id },
    });

    if (!product) {
      throw new NotFoundException(`Product with ID "${id}" not found`);
    }

    if (product.vendorId !== vendorId) {
      throw new BadRequestException('You can only update your own products');
    }

    const updatedProduct = repository.merge(product, updateProductDto);
    return repository.save(updatedProduct);
  }

  async remove(id: string, tenantId: string, vendorId: string): Promise<void> {
    const tenantConnection = await this.tenantsService.getTenantConnection(tenantId);
    const repository = tenantConnection.dataSource.getRepository(Product);

    const product = await repository.findOne({
      where: { id },
    });

    if (!product) {
      throw new NotFoundException(`Product with ID "${id}" not found`);
    }

    if (product.vendorId !== vendorId) {
      throw new BadRequestException('You can only delete your own products');
    }

    await repository.remove(product);
  }
}
