import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateProductsTable1748092685908 implements MigrationInterface {
    name = 'CreateProductsTable1748092685908'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create products table for tenant database
        await queryRunner.query(`
            CREATE TABLE "products" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying NOT NULL,
                "description" text,
                "price" numeric(10,2) NOT NULL,
                "stock" integer NOT NULL DEFAULT '0',
                "images" text array DEFAULT '{}',
                "isActive" boolean NOT NULL DEFAULT true,
                "categoryId" uuid,
                "vendorId" uuid NOT NULL,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_products" PRIMARY KEY ("id")
            )
        `);

        // Add foreign key constraint for categories
        // The reference is to the main database's categories table
        await queryRunner.query(`
            ALTER TABLE "products" ADD CONSTRAINT "FK_product_category"
            FOREIGN KEY ("categoryId") REFERENCES "${process.env.DB_MAIN_DATABASE || 'postgres'}"."categories"("id")
            ON DELETE SET NULL ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key first
        await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "FK_product_category"`);
        
        // Drop products table
        await queryRunner.query(`DROP TABLE "products"`);
    }
}
