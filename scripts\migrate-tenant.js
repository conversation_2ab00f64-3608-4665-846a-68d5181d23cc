#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to run migrations for a specific tenant database
 * Usage: node scripts/migrate-tenant.js <tenant_database_name>
 * Example: node scripts/migrate-tenant.js tenant_demo_one
 */

const { spawn } = require('child_process');
const { Client } = require('pg');
require('dotenv').config();

async function getTenantDatabases() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_MAIN_DATABASE || 'postgres',
  });

  try {
    await client.connect();
    const res = await client.query(
      'SELECT "databaseName", "subdomain", "storeName" FROM vendors WHERE "databaseName" IS NOT NULL ORDER BY "storeName"'
    );
    return res.rows;
  } catch (error) {
    console.error('Error fetching tenant databases:', error.message);
    return [];
  } finally {
    await client.end();
  }
}

async function runMigration(tenantDb) {
  return new Promise((resolve, reject) => {
    const isWindows = process.platform === 'win32';
    const command = isWindows ? 'cmd' : 'sh';
    const args = isWindows 
      ? ['/c', `set TENANT_DB=${tenantDb} && npm run migration:run:tenant`]
      : ['-c', `TENANT_DB=${tenantDb} npm run migration:run:tenant`];

    console.log(`\n🔄 Running migration for tenant: ${tenantDb}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      env: { ...process.env, TENANT_DB: tenantDb }
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ Migration completed for tenant: ${tenantDb}`);
        resolve();
      } else {
        console.log(`❌ Migration failed for tenant: ${tenantDb}`);
        reject(new Error(`Migration failed with code ${code}`));
      }
    });
  });
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('📋 Available tenant databases:');
    const tenants = await getTenantDatabases();
    
    if (tenants.length === 0) {
      console.log('No tenant databases found.');
      return;
    }

    tenants.forEach((tenant, index) => {
      console.log(`${index + 1}. ${tenant.databaseName} (${tenant.storeName} - ${tenant.subdomain})`);
    });
    
    console.log('\nUsage:');
    console.log('  node scripts/migrate-tenant.js <tenant_database_name>');
    console.log('  node scripts/migrate-tenant.js all');
    console.log('\nExamples:');
    console.log('  node scripts/migrate-tenant.js tenant_demo_one');
    console.log('  node scripts/migrate-tenant.js all');
    return;
  }

  const targetTenant = args[0];

  if (targetTenant === 'all') {
    console.log('🚀 Running migrations for all tenant databases...');
    const tenants = await getTenantDatabases();
    
    for (const tenant of tenants) {
      try {
        await runMigration(tenant.databaseName);
      } catch (error) {
        console.error(`Failed to migrate ${tenant.databaseName}:`, error.message);
      }
    }
    
    console.log('\n✅ All tenant migrations completed!');
  } else {
    // Validate that the tenant database exists
    const tenants = await getTenantDatabases();
    const tenantExists = tenants.some(t => t.databaseName === targetTenant);
    
    if (!tenantExists) {
      console.error(`❌ Tenant database '${targetTenant}' not found.`);
      console.log('\nAvailable tenant databases:');
      tenants.forEach(tenant => {
        console.log(`  - ${tenant.databaseName} (${tenant.storeName})`);
      });
      process.exit(1);
    }

    await runMigration(targetTenant);
  }
}

main().catch(console.error);
