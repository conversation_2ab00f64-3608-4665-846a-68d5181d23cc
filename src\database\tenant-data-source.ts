import { DataSource, DataSourceOptions } from 'typeorm';
import * as dotenv from 'dotenv';
import { Client } from 'pg';

dotenv.config();

/**
 * Gets the TypeORM configuration for a tenant database
 * @param databaseName The name of the tenant database
 * @returns DataSourceOptions configuration
 */
export const getTenantDataSourceConfig = (databaseName: string): DataSourceOptions => ({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: databaseName,
  entities: [
    __dirname + '/../modules/products/entities/*.entity{.ts,.js}',
    __dirname + '/../modules/orders/entities/*.entity{.ts,.js}',
    __dirname + '/../modules/categories/entities/*.entity{.ts,.js}',
  ],
  migrations: [__dirname + '/migrations/tenant/*{.ts,.js}'],
  synchronize: false,
  logging: process.env.DB_LOGGING === 'true',
});

/**
 * Gets the PostgreSQL client configuration for connecting to databases
 * @param databaseName Optional database name (defaults to main database)
 * @returns Client configuration
 */
const getPgClientConfig = (databaseName?: string) => ({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT ? parseInt(process.env.DB_PORT, 10) : 5432,
  user: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: databaseName || process.env.DB_MAIN_DATABASE || 'postgres',
});

/**
 * Migrates all tenant databases listed in the vendors table (main DB),
 * only if not already migrated.
 */
export async function migrateAllTenants() {
  console.log('Starting tenant migrations...');

  // 1. Connect to main DB and get all tenant database names
  const mainClient = new Client(getPgClientConfig());
  try {
    await mainClient.connect();
    const res = await mainClient.query(
      'SELECT "databaseName" FROM vendors WHERE "databaseName" IS NOT NULL'
    );
    const tenantDbs = res.rows.map(row => row.databaseName);
    
    if (tenantDbs.length === 0) {
      console.log('No tenant databases found to migrate.');
      return;
    }

    console.log(`Found ${tenantDbs.length} tenant databases to process`);

    // 2. For each tenant DB, check if migrations have run, and run if not
    for (const dbName of tenantDbs) {
      const tenantClient = new Client(getPgClientConfig(dbName));
      try {
        await tenantClient.connect();
        
        // Check if migrations table exists and has entries
        const tableRes = await tenantClient.query(
          `SELECT to_regclass('public.tenant_migrations') as exists`
        );
        
        let alreadyMigrated = false;
        if (tableRes.rows[0].exists) {
          const migRes = await tenantClient.query(
            'SELECT COUNT(*) FROM tenant_migrations'
          );
          alreadyMigrated = parseInt(migRes.rows[0].count, 10) > 0;
        }

        if (alreadyMigrated) {
          console.log(`✓ Migrations already ran for tenant: ${dbName}`);
          continue;
        }

        // Run migrations for this tenant DB
        console.log(`⌛ Running migrations for tenant: ${dbName}`);
        const dataSource = new DataSource(getTenantDataSourceConfig(dbName));
        
        try {
          await dataSource.initialize();
          await dataSource.runMigrations();
          console.log(`✓ Migrations completed for tenant: ${dbName}`);
        } finally {
          await dataSource.destroy();
        }
      } catch (err) {
        console.error(`✗ Migration failed for tenant: ${dbName}`, err);
      } finally {
        await tenantClient.end();
      }
    }
  } catch (err) {
    console.error('Failed to process tenant migrations:', err);
    throw err;
  } finally {
    await mainClient.end();
  }

  console.log('✅ All tenant migrations processed');
}

/**
 * Helper function to run the migrations from command line
 */
export async function runTenantMigrations() {
  try {
    await migrateAllTenants();
    process.exit(0);
  } catch (error) {
    console.error('❌ Tenant migration failed:', error);
    process.exit(1);
  }
}

// Execute if run directly (for command line usage)
if (require.main === module) {
  runTenantMigrations();
}

export const AppDataSource = new DataSource(getTenantDataSourceConfig(process.env.DB_MAIN_DATABASE || 'postgres'));