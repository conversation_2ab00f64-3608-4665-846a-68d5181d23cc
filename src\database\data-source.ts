import { DataSource, DataSourceOptions } from 'typeorm';
import * as dotenv from 'dotenv';

dotenv.config();

export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: process.env.DB_MAIN_DATABASE || 'postgres',
  entities: [
    __dirname + '/../modules/users/entities/*.entity{.ts,.js}',
    __dirname + '/../modules/categories/entities/*.entity{.ts,.js}',
  ], // Only main database entities
  migrations: [__dirname + '/migrations/main/*{.ts,.js}'], // Main database migrations in dedicated folder
  synchronize: false,
  dropSchema: false,
  logging: true, // Always true for development
};

const dataSource = new DataSource(dataSourceOptions);
export default dataSource;
